<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI计算主权 - 全球地缘政治分析</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/framer-motion/10.16.4/framer-motion.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
        
        * {
            font-family: 'Inter', sans-serif;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .glass-effect {
            backdrop-filter: blur(20px);
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .bento-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }
        
        .bento-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .highlight-blue {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.8) 0%, rgba(59, 130, 246, 0.2) 100%);
        }
        
        .highlight-purple {
            background: linear-gradient(135deg, rgba(147, 51, 234, 0.8) 0%, rgba(147, 51, 234, 0.2) 100%);
        }
        
        .highlight-green {
            background: linear-gradient(135deg, rgba(34, 197, 94, 0.8) 0%, rgba(34, 197, 94, 0.2) 100%);
        }
        
        .highlight-orange {
            background: linear-gradient(135deg, rgba(249, 115, 22, 0.8) 0%, rgba(249, 115, 22, 0.2) 100%);
        }
        
        .mega-text {
            font-size: clamp(4rem, 12vw, 12rem);
            font-weight: 900;
            line-height: 0.8;
        }
        
        .large-text {
            font-size: clamp(2rem, 6vw, 6rem);
            font-weight: 800;
        }
        
        .parallax-element {
            will-change: transform;
        }
        
        .chart-container {
            position: relative;
            height: 300px;
            width: 100%;
        }
        
        .scroll-indicator {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #3b82f6, #8b5cf6, #22c55e, #f97316);
            transform-origin: left;
            z-index: 1000;
        }
    </style>
</head>
<body class="bg-gray-50 overflow-x-hidden">
    <!-- Scroll Progress Indicator -->
    <div class="scroll-indicator" id="scrollProgress"></div>
    
    <!-- Hero Section -->
    <section class="min-h-screen gradient-bg relative overflow-hidden">
        <div class="absolute inset-0 opacity-10">
            <div class="absolute top-20 left-20 w-64 h-64 bg-white rounded-full blur-3xl"></div>
            <div class="absolute bottom-20 right-20 w-96 h-96 bg-blue-300 rounded-full blur-3xl"></div>
        </div>
        
        <div class="container mx-auto px-6 py-20 relative z-10">
            <div class="text-center text-white">
                <div class="parallax-element" data-speed="0.5">
                    <h1 class="mega-text mb-8">AI计算主权</h1>
                    <p class="text-xl md:text-2xl mb-4 opacity-90">The Geopolitics of AI Compute Sovereignty</p>
                    <p class="text-lg opacity-80 max-w-4xl mx-auto leading-relaxed">
                        基于全球九大云服务提供商的深度研究，揭示AI计算资源分布的全球不平等与地缘政治复杂性
                    </p>
                </div>
                
                <div class="mt-16 parallax-element" data-speed="0.3">
                    <div class="glass-effect rounded-2xl p-8 max-w-2xl mx-auto">
                        <div class="flex items-center justify-center space-x-8">
                            <div class="text-center">
                                <div class="text-4xl font-bold">225</div>
                                <div class="text-sm opacity-80">云区域总数</div>
                            </div>
                            <div class="text-center">
                                <div class="text-4xl font-bold">33</div>
                                <div class="text-sm opacity-80">拥有AI计算的国家</div>
                            </div>
                            <div class="text-center">
                                <div class="text-4xl font-bold">12%</div>
                                <div class="text-sm opacity-80">联合国成员国占比</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Three Levels Framework -->
    <section class="py-20 bg-white">
        <div class="container mx-auto px-6">
            <div class="text-center mb-16 parallax-element" data-speed="0.2">
                <h2 class="large-text text-gray-900 mb-6">三层分析框架</h2>
                <p class="text-xl text-gray-600">Three-Level Framework for Compute Sovereignty</p>
            </div>
            
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Level 1 -->
                <div class="bento-card rounded-3xl p-8 parallax-element" data-speed="0.1">
                    <div class="highlight-blue rounded-2xl p-6 mb-6">
                        <i class="fas fa-map-marker-alt text-4xl text-white mb-4"></i>
                        <div class="text-6xl font-black text-white">01</div>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-4">领土管辖权</h3>
                    <p class="text-sm text-gray-500 mb-4">Territorial Jurisdiction</p>
                    <p class="text-gray-700 leading-relaxed">
                        AI计算数据中心的物理位置控制，符合威斯特伐利亚主权概念，使政府能够监管基础设施并执行KYC等控制措施。
                    </p>
                    <div class="mt-6 p-4 bg-gray-50 rounded-xl">
                        <div class="text-3xl font-bold text-blue-600">132</div>
                        <div class="text-sm text-gray-600">加速器启用区域</div>
                    </div>
                </div>
                
                <!-- Level 2 -->
                <div class="bento-card rounded-3xl p-8 parallax-element" data-speed="0.15">
                    <div class="highlight-purple rounded-2xl p-6 mb-6">
                        <i class="fas fa-building text-4xl text-white mb-4"></i>
                        <div class="text-6xl font-black text-white">02</div>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-4">云服务商国籍</h3>
                    <p class="text-sm text-gray-500 mb-4">Cloud Provider Nationality</p>
                    <p class="text-gray-700 leading-relaxed">
                        运营AI数据中心的公司所有权，涉及国内能力建设和避免外国政府干预/监控的双重考量。
                    </p>
                    <div class="mt-6 grid grid-cols-2 gap-4">
                        <div class="p-3 bg-gray-50 rounded-xl text-center">
                            <div class="text-2xl font-bold text-purple-600">18</div>
                            <div class="text-xs text-gray-600">单一对齐国家</div>
                        </div>
                        <div class="p-3 bg-gray-50 rounded-xl text-center">
                            <div class="text-2xl font-bold text-purple-600">12</div>
                            <div class="text-xs text-gray-600">多元化对冲国家</div>
                        </div>
                    </div>
                </div>
                
                <!-- Level 3 -->
                <div class="bento-card rounded-3xl p-8 parallax-element" data-speed="0.2">
                    <div class="highlight-green rounded-2xl p-6 mb-6">
                        <i class="fas fa-microchip text-4xl text-white mb-4"></i>
                        <div class="text-6xl font-black text-white">03</div>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-4">芯片供应商国籍</h3>
                    <p class="text-sm text-gray-500 mb-4">Accelerator Vendor Nationality</p>
                    <p class="text-gray-700 leading-relaxed">
                        最细粒度层面，关注驱动数据中心的先进AI芯片供应链控制，NVIDIA占据80-95%的市场份额。
                    </p>
                    <div class="mt-6 p-4 bg-gray-50 rounded-xl">
                        <div class="text-3xl font-bold text-green-600">95.5%</div>
                        <div class="text-sm text-gray-600">美国芯片驱动的区域</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Global Distribution -->
    <section class="py-20 bg-gray-100">
        <div class="container mx-auto px-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div class="parallax-element" data-speed="0.1">
                    <h2 class="large-text text-gray-900 mb-8">全球分布现状</h2>
                    <p class="text-lg text-gray-600 mb-8">Global AI Compute Distribution</p>
                    
                    <div class="space-y-6">
                        <div class="bento-card rounded-2xl p-6">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="text-xl font-bold text-gray-900">美国</h3>
                                    <p class="text-sm text-gray-500">United States</p>
                                </div>
                                <div class="text-right">
                                    <div class="text-3xl font-bold text-blue-600">26</div>
                                    <div class="text-sm text-gray-600">计算区域</div>
                                </div>
                            </div>
                            <div class="mt-4 bg-blue-100 rounded-full h-3">
                                <div class="bg-blue-600 h-3 rounded-full" style="width: 85%"></div>
                            </div>
                        </div>
                        
                        <div class="bento-card rounded-2xl p-6">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="text-xl font-bold text-gray-900">中国</h3>
                                    <p class="text-sm text-gray-500">China</p>
                                </div>
                                <div class="text-right">
                                    <div class="text-3xl font-bold text-red-600">22</div>
                                    <div class="text-sm text-gray-600">计算区域</div>
                                </div>
                            </div>
                            <div class="mt-4 bg-red-100 rounded-full h-3">
                                <div class="bg-red-600 h-3 rounded-full" style="width: 72%"></div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="parallax-element" data-speed="0.15">
                    <div class="bento-card rounded-3xl p-8">
                        <h3 class="text-2xl font-bold text-gray-900 mb-6">计算资源分布</h3>
                        <div class="chart-container">
                            <canvas id="distributionChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Strategic Approaches -->
    <section class="py-20 bg-white">
        <div class="container mx-auto px-6">
            <div class="text-center mb-16 parallax-element" data-speed="0.1">
                <h2 class="large-text text-gray-900 mb-6">战略选择</h2>
                <p class="text-xl text-gray-600">Strategic Approaches to Compute Sovereignty</p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <!-- Aligning -->
                <div class="bento-card rounded-3xl p-6 parallax-element" data-speed="0.05">
                    <div class="highlight-blue rounded-xl p-4 mb-4">
                        <i class="fas fa-handshake text-2xl text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-2">对齐策略</h3>
                    <p class="text-sm text-gray-500 mb-3">Aligning Strategy</p>
                    <p class="text-gray-700 text-sm leading-relaxed">
                        依赖单一外国盟友的云服务提供商，如澳大利亚与AWS的合作
                    </p>
                    <div class="mt-4 text-center">
                        <div class="text-2xl font-bold text-blue-600">18</div>
                        <div class="text-xs text-gray-600">国家采用</div>
                    </div>
                </div>
                
                <!-- Hedging -->
                <div class="bento-card rounded-3xl p-6 parallax-element" data-speed="0.1">
                    <div class="highlight-purple rounded-xl p-4 mb-4">
                        <i class="fas fa-balance-scale text-2xl text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-2">对冲策略</h3>
                    <p class="text-sm text-gray-500 mb-3">Hedging Strategy</p>
                    <p class="text-gray-700 text-sm leading-relaxed">
                        通过多元化实现战略自主，如新加坡同时托管美中云服务商
                    </p>
                    <div class="mt-4 text-center">
                        <div class="text-2xl font-bold text-purple-600">12</div>
                        <div class="text-xs text-gray-600">国家采用</div>
                    </div>
                </div>
                
                <!-- Regional -->
                <div class="bento-card rounded-3xl p-6 parallax-element" data-speed="0.15">
                    <div class="highlight-green rounded-xl p-4 mb-4">
                        <i class="fas fa-users text-2xl text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-2">区域合作</h3>
                    <p class="text-sm text-gray-500 mb-3">Regional Cooperation</p>
                    <p class="text-gray-700 text-sm leading-relaxed">
                        如欧洲高性能计算联合计划，共享资源和成本分摊
                    </p>
                    <div class="mt-4 text-center">
                        <div class="text-2xl font-bold text-green-600">EU</div>
                        <div class="text-xs text-gray-600">EuroHPC示例</div>
                    </div>
                </div>
                
                <!-- Friendshoring -->
                <div class="bento-card rounded-3xl p-6 parallax-element" data-speed="0.2">
                    <div class="highlight-orange rounded-xl p-4 mb-4">
                        <i class="fas fa-shipping-fast text-2xl text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-2">友岸外包</h3>
                    <p class="text-sm text-gray-500 mb-3">Friendshoring</p>
                    <p class="text-gray-700 text-sm leading-relaxed">
                        选择可信赖的外国AI加速器芯片供应商，实现供应链多元化
                    </p>
                    <div class="mt-4 text-center">
                        <div class="text-2xl font-bold text-orange-600">95%</div>
                        <div class="text-xs text-gray-600">NVIDIA依赖度</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Key Findings -->
    <section class="py-20 bg-gray-900 text-white">
        <div class="container mx-auto px-6">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <div class="lg:col-span-2 parallax-element" data-speed="0.1">
                    <h2 class="large-text mb-8">核心发现</h2>
                    <p class="text-xl text-gray-300 mb-8">Key Empirical Findings</p>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="glass-effect rounded-2xl p-6">
                            <div class="text-4xl font-bold text-blue-400 mb-2">80-95%</div>
                            <h3 class="text-lg font-semibold mb-2">NVIDIA市场份额</h3>
                            <p class="text-sm text-gray-300">在AI加速器市场的近乎垄断地位</p>
                        </div>

                        <div class="glass-effect rounded-2xl p-6">
                            <div class="text-4xl font-bold text-purple-400 mb-2">24</div>
                            <h3 class="text-lg font-semibold mb-2">训练级计算国家</h3>
                            <p class="text-sm text-gray-300">拥有训练相关AI计算能力的国家数量</p>
                        </div>

                        <div class="glass-effect rounded-2xl p-6">
                            <div class="text-4xl font-bold text-green-400 mb-2">9</div>
                            <h3 class="text-lg font-semibold mb-2">主要云服务商</h3>
                            <p class="text-sm text-gray-300">美国、中国、欧洲的领先公共云提供商</p>
                        </div>

                        <div class="glass-effect rounded-2xl p-6">
                            <div class="text-4xl font-bold text-orange-400 mb-2">43</div>
                            <h3 class="text-lg font-semibold mb-2">覆盖国家总数</h3>
                            <p class="text-sm text-gray-300">研究涵盖的全球国家数量</p>
                        </div>
                    </div>
                </div>

                <div class="parallax-element" data-speed="0.15">
                    <div class="glass-effect rounded-3xl p-8">
                        <h3 class="text-2xl font-bold mb-6">地理分布</h3>
                        <div class="space-y-4">
                            <div class="flex justify-between items-center">
                                <span class="text-gray-300">北美</span>
                                <div class="flex items-center space-x-2">
                                    <div class="w-20 bg-gray-700 rounded-full h-2">
                                        <div class="bg-blue-400 h-2 rounded-full" style="width: 90%"></div>
                                    </div>
                                    <span class="text-sm">90%</span>
                                </div>
                            </div>

                            <div class="flex justify-between items-center">
                                <span class="text-gray-300">亚洲</span>
                                <div class="flex items-center space-x-2">
                                    <div class="w-20 bg-gray-700 rounded-full h-2">
                                        <div class="bg-purple-400 h-2 rounded-full" style="width: 75%"></div>
                                    </div>
                                    <span class="text-sm">75%</span>
                                </div>
                            </div>

                            <div class="flex justify-between items-center">
                                <span class="text-gray-300">欧洲</span>
                                <div class="flex items-center space-x-2">
                                    <div class="w-20 bg-gray-700 rounded-full h-2">
                                        <div class="bg-green-400 h-2 rounded-full" style="width: 60%"></div>
                                    </div>
                                    <span class="text-sm">60%</span>
                                </div>
                            </div>

                            <div class="flex justify-between items-center">
                                <span class="text-gray-300">中东</span>
                                <div class="flex items-center space-x-2">
                                    <div class="w-20 bg-gray-700 rounded-full h-2">
                                        <div class="bg-orange-400 h-2 rounded-full" style="width: 30%"></div>
                                    </div>
                                    <span class="text-sm">30%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Policy Implications -->
    <section class="py-20 bg-white">
        <div class="container mx-auto px-6">
            <div class="text-center mb-16 parallax-element" data-speed="0.1">
                <h2 class="large-text text-gray-900 mb-6">政策影响</h2>
                <p class="text-xl text-gray-600">Policy Implications and Trade-offs</p>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
                <!-- Benefits -->
                <div class="parallax-element" data-speed="0.05">
                    <div class="bento-card rounded-3xl p-8">
                        <div class="highlight-green rounded-2xl p-6 mb-6 text-center">
                            <i class="fas fa-check-circle text-4xl text-white mb-2"></i>
                            <h3 class="text-2xl font-bold text-white">优势</h3>
                            <p class="text-sm text-green-100">Benefits</p>
                        </div>

                        <div class="space-y-4">
                            <div class="flex items-start space-x-3">
                                <i class="fas fa-shield-alt text-green-600 mt-1"></i>
                                <div>
                                    <h4 class="font-semibold text-gray-900">供应安全</h4>
                                    <p class="text-sm text-gray-600">增强对关键AI基础设施的控制</p>
                                </div>
                            </div>

                            <div class="flex items-start space-x-3">
                                <i class="fas fa-gavel text-green-600 mt-1"></i>
                                <div>
                                    <h4 class="font-semibold text-gray-900">司法管辖</h4>
                                    <p class="text-sm text-gray-600">数据隐私和安全法规的有效执行</p>
                                </div>
                            </div>

                            <div class="flex items-start space-x-3">
                                <i class="fas fa-chart-line text-green-600 mt-1"></i>
                                <div>
                                    <h4 class="font-semibold text-gray-900">经济发展</h4>
                                    <p class="text-sm text-gray-600">税收收入、建设就业和可再生能源投资</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Drawbacks -->
                <div class="parallax-element" data-speed="0.1">
                    <div class="bento-card rounded-3xl p-8">
                        <div class="highlight-orange rounded-2xl p-6 mb-6 text-center">
                            <i class="fas fa-exclamation-triangle text-4xl text-white mb-2"></i>
                            <h3 class="text-2xl font-bold text-white">挑战</h3>
                            <p class="text-sm text-orange-100">Drawbacks</p>
                        </div>

                        <div class="space-y-4">
                            <div class="flex items-start space-x-3">
                                <i class="fas fa-bolt text-orange-600 mt-1"></i>
                                <div>
                                    <h4 class="font-semibold text-gray-900">资源消耗</h4>
                                    <p class="text-sm text-gray-600">大量能源、水资源和土地需求</p>
                                </div>
                            </div>

                            <div class="flex items-start space-x-3">
                                <i class="fas fa-users text-orange-600 mt-1"></i>
                                <div>
                                    <h4 class="font-semibold text-gray-900">就业创造有限</h4>
                                    <p class="text-sm text-gray-600">相对于资源投入的就业机会较少</p>
                                </div>
                            </div>

                            <div class="flex items-start space-x-3">
                                <i class="fas fa-cogs text-orange-600 mt-1"></i>
                                <div>
                                    <h4 class="font-semibold text-gray-900">监管复杂性</h4>
                                    <p class="text-sm text-gray-600">与外国云服务商的法规协调难题</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Conclusion -->
    <section class="py-20 gradient-bg text-white">
        <div class="container mx-auto px-6 text-center">
            <div class="parallax-element" data-speed="0.1">
                <h2 class="large-text mb-8">结论与展望</h2>
                <p class="text-xl mb-8 opacity-90">Conclusion and Future Outlook</p>

                <div class="max-w-4xl mx-auto">
                    <p class="text-lg leading-relaxed mb-8 opacity-90">
                        计算主权不是一个单一的二元条件，而是一个多层面的概念，具有不同的解释和影响。
                        实证数据揭示了AI计算分布的全球不对称性，少数"计算拥有者"高度集中。
                        各国政府在追求计算主权时面临复杂的权衡和战略选择。
                    </p>

                    <div class="glass-effect rounded-2xl p-8 max-w-2xl mx-auto">
                        <h3 class="text-2xl font-bold mb-4">关键洞察</h3>
                        <p class="text-lg opacity-90">
                            计算主权的追求是一个"由潜在价值观和权力动态塑造的政治项目"，
                            需要政策制定者的仔细考虑和批判性分析。
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="container mx-auto px-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div>
                    <h3 class="text-xl font-bold mb-4">研究来源</h3>
                    <p class="text-gray-400 text-sm">
                        基于"ssrn-5312977.pdf"研究报告<br>
                        发布日期：2024-11-20
                    </p>
                </div>

                <div>
                    <h3 class="text-xl font-bold mb-4">数据覆盖</h3>
                    <p class="text-gray-400 text-sm">
                        九大云服务提供商<br>
                        43个国家，225个云区域
                    </p>
                </div>

                <div>
                    <h3 class="text-xl font-bold mb-4">分析框架</h3>
                    <p class="text-gray-400 text-sm">
                        三层计算主权概念<br>
                        地缘政治视角分析
                    </p>
                </div>
            </div>

            <div class="border-t border-gray-800 mt-8 pt-8 text-center">
                <p class="text-gray-400 text-sm">
                    © 2024 AI计算主权研究报告 | 基于学术研究的可视化展示
                </p>
            </div>
        </div>
    </footer>

    <script>
        // Scroll Progress Indicator
        window.addEventListener('scroll', () => {
            const scrollProgress = document.getElementById('scrollProgress');
            const scrollPercent = (window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100;
            scrollProgress.style.transform = `scaleX(${scrollPercent / 100})`;
        });

        // Parallax Effect
        window.addEventListener('scroll', () => {
            const parallaxElements = document.querySelectorAll('.parallax-element');
            const scrollTop = window.pageYOffset;

            parallaxElements.forEach(element => {
                const speed = element.dataset.speed || 0.1;
                const yPos = -(scrollTop * speed);
                element.style.transform = `translateY(${yPos}px)`;
            });
        });

        // Chart.js - Distribution Chart
        const ctx = document.getElementById('distributionChart').getContext('2d');
        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['美国', '中国', '欧洲', '其他'],
                datasets: [{
                    data: [26, 22, 15, 12],
                    backgroundColor: [
                        'rgba(59, 130, 246, 0.8)',
                        'rgba(239, 68, 68, 0.8)',
                        'rgba(34, 197, 94, 0.8)',
                        'rgba(156, 163, 175, 0.8)'
                    ],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            font: {
                                size: 14
                            }
                        }
                    }
                }
            }
        });

        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe all bento cards
        document.querySelectorAll('.bento-card').forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            card.style.transition = 'all 0.6s ease';
            observer.observe(card);
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>
