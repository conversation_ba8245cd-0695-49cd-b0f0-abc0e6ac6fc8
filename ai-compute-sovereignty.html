<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI计算主权 - 全球地缘政治分析</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/framer-motion/10.16.4/framer-motion.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
        
        * {
            font-family: 'Inter', sans-serif;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .glass-effect {
            backdrop-filter: blur(20px);
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .bento-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }
        
        .bento-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .highlight-blue {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.8) 0%, rgba(59, 130, 246, 0.2) 100%);
        }
        
        .highlight-purple {
            background: linear-gradient(135deg, rgba(147, 51, 234, 0.8) 0%, rgba(147, 51, 234, 0.2) 100%);
        }
        
        .highlight-green {
            background: linear-gradient(135deg, rgba(34, 197, 94, 0.8) 0%, rgba(34, 197, 94, 0.2) 100%);
        }
        
        .highlight-orange {
            background: linear-gradient(135deg, rgba(249, 115, 22, 0.8) 0%, rgba(249, 115, 22, 0.2) 100%);
        }
        
        .mega-text {
            font-size: clamp(4rem, 12vw, 12rem);
            font-weight: 900;
            line-height: 0.8;
        }
        
        .large-text {
            font-size: clamp(2rem, 6vw, 6rem);
            font-weight: 800;
        }
        
        .parallax-element {
            will-change: transform;
        }
        
        .chart-container {
            position: relative;
            height: 300px;
            width: 100%;
        }
        
        .scroll-indicator {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #3b82f6, #8b5cf6, #22c55e, #f97316);
            transform-origin: left;
            z-index: 1000;
        }
    </style>
</head>
<body class="bg-gray-50 overflow-x-hidden">
    <!-- Scroll Progress Indicator -->
    <div class="scroll-indicator" id="scrollProgress"></div>
    
    <!-- Hero Section -->
    <section class="min-h-screen gradient-bg relative overflow-hidden">
        <div class="absolute inset-0 opacity-10">
            <div class="absolute top-20 left-20 w-64 h-64 bg-white rounded-full blur-3xl"></div>
            <div class="absolute bottom-20 right-20 w-96 h-96 bg-blue-300 rounded-full blur-3xl"></div>
        </div>

        <div class="container mx-auto px-6 py-12 relative z-10">
            <div class="text-center text-white mb-12">
                <div class="parallax-element" data-speed="0.5">
                    <h1 class="mega-text mb-6">AI计算主权</h1>
                    <p class="text-xl md:text-2xl mb-6 opacity-90">The Geopolitics of AI Compute Sovereignty</p>
                </div>
            </div>

            <!-- Executive Summary -->
            <div class="max-w-6xl mx-auto">
                <div class="glass-effect rounded-3xl p-8 mb-8">
                    <h2 class="text-3xl font-bold text-white mb-6 text-center">执行摘要 Executive Summary</h2>
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 text-white">
                        <div>
                            <p class="text-lg leading-relaxed mb-4 opacity-90">
                                本研究提出了一个三层框架来分析"计算主权"：<strong>(1) AI计算的领土拥有</strong>、<strong>(2) 云服务提供商的国籍</strong>、<strong>(3) AI加速器芯片供应商的国籍</strong>。
                            </p>
                            <p class="text-lg leading-relaxed mb-4 opacity-90">
                                基于对九家领先公共云提供商（美国、中国和欧洲）的普查，研究揭示了AI计算分布的显著全球差异，突出了各国追求AI计算主权过程中经济、安全和地缘政治因素的复杂相互作用。
                            </p>
                        </div>
                        <div>
                            <p class="text-lg leading-relaxed mb-4 opacity-90">
                                This study proposes a three-level framework to analyze "compute sovereignty": territorial possession of AI compute, nationality of cloud provider ownership, and nationality of AI accelerator chip vendors.
                            </p>
                            <p class="text-lg leading-relaxed opacity-90">
                                The research reveals significant global disparities in AI compute distribution and highlights the complex interplay of economic, security, and geopolitical factors in countries' pursuit of AI compute sovereignty.
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Key Statistics -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div class="glass-effect rounded-2xl p-6 text-center text-white">
                        <div class="text-4xl font-bold mb-2">225</div>
                        <div class="text-sm opacity-80">云区域总数</div>
                        <div class="text-xs opacity-60">Cloud Regions</div>
                    </div>
                    <div class="glass-effect rounded-2xl p-6 text-center text-white">
                        <div class="text-4xl font-bold mb-2">33</div>
                        <div class="text-sm opacity-80">拥有AI计算国家</div>
                        <div class="text-xs opacity-60">Countries with AI Compute</div>
                    </div>
                    <div class="glass-effect rounded-2xl p-6 text-center text-white">
                        <div class="text-4xl font-bold mb-2">24</div>
                        <div class="text-sm opacity-80">训练级计算国家</div>
                        <div class="text-xs opacity-60">Training-capable Countries</div>
                    </div>
                    <div class="glass-effect rounded-2xl p-6 text-center text-white">
                        <div class="text-4xl font-bold mb-2">12%</div>
                        <div class="text-sm opacity-80">联合国成员占比</div>
                        <div class="text-xs opacity-60">UN Member States</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Defining Compute Sovereignty -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-6">
            <div class="max-w-6xl mx-auto">
                <div class="text-center mb-12">
                    <h2 class="large-text text-gray-900 mb-6">定义"计算主权"</h2>
                    <p class="text-xl text-gray-600">Defining "Compute Sovereignty" and its Three Levels</p>
                </div>

                <!-- Concept Introduction -->
                <div class="bento-card rounded-3xl p-8 mb-12">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <div>
                            <h3 class="text-2xl font-bold text-gray-900 mb-4">数字时代的主权概念</h3>
                            <p class="text-gray-700 leading-relaxed mb-4">
                                "主权"概念——政府在其领土内的最高权威——与数字技术有着复杂且不断演变的关系。"数字主权"最初专注于对互联网治理的国家控制，但最近已与AI治理和"计算治理"交织在一起。
                            </p>
                            <p class="text-gray-700 leading-relaxed">
                                <strong>"计算主权"</strong>作为数字主权的一个子类别，描述了"确保获得AI计算资源的感知需求"。这一概念反映了各国对控制关键AI基础设施的日益关注。
                            </p>
                        </div>
                        <div>
                            <h3 class="text-2xl font-bold text-gray-900 mb-4">Sovereignty in Digital Age</h3>
                            <p class="text-gray-700 leading-relaxed mb-4">
                                The concept of "sovereignty" – a government's supreme authority within its territory – has a complex and evolving relationship with digital technologies. "Digital sovereignty" initially focused on national control over internet governance.
                            </p>
                            <p class="text-gray-700 leading-relaxed">
                                <strong>"Compute Sovereignty"</strong> describes the "perceived need to secure access to AI compute resources," reflecting growing concerns about controlling critical AI infrastructure.
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Three Levels Framework -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- Level 1 -->
                    <div class="bento-card rounded-3xl p-8">
                        <div class="highlight-blue rounded-2xl p-6 mb-6 text-center">
                            <i class="fas fa-map-marker-alt text-4xl text-white mb-4"></i>
                            <div class="text-6xl font-black text-white">01</div>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-900 mb-2">领土管辖权</h3>
                        <p class="text-sm text-gray-500 mb-4">Territorial Jurisdiction over AI Compute</p>
                        <p class="text-gray-700 leading-relaxed mb-4">
                            这是最广泛的解释，意味着AI计算资源物理位置在国家边界内。它符合威斯特伐利亚主权概念，使政府能够监管基础设施并执行KYC检查等控制措施。
                        </p>
                        <div class="bg-gray-50 rounded-xl p-4">
                            <div class="text-center mb-3">
                                <div class="text-3xl font-bold text-blue-600">132</div>
                                <div class="text-sm text-gray-600">加速器启用区域</div>
                            </div>
                            <p class="text-xs text-gray-600 text-center">
                                在43个国家的225个云区域中，只有33个国家拥有AI计算能力
                            </p>
                        </div>
                    </div>

                    <!-- Level 2 -->
                    <div class="bento-card rounded-3xl p-8">
                        <div class="highlight-purple rounded-2xl p-6 mb-6 text-center">
                            <i class="fas fa-building text-4xl text-white mb-4"></i>
                            <div class="text-6xl font-black text-white">02</div>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-900 mb-2">云服务商国籍</h3>
                        <p class="text-sm text-gray-500 mb-4">Nationality of AI Compute Cloud Providers</p>
                        <p class="text-gray-700 leading-relaxed mb-4">
                            这一层面考虑运营AI数据中心的公司所有权。它涉及"自由能力"（国内能力）和"免于干预"（外国政府干预/监控）的双重关切。
                        </p>
                        <div class="bg-gray-50 rounded-xl p-4">
                            <div class="grid grid-cols-2 gap-3 mb-3">
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-purple-600">18</div>
                                    <div class="text-xs text-gray-600">对齐国家</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-purple-600">12</div>
                                    <div class="text-xs text-gray-600">对冲国家</div>
                                </div>
                            </div>
                            <p class="text-xs text-gray-600 text-center">
                                全球市场由少数大型提供商主导，主要是美国和中国公司
                            </p>
                        </div>
                    </div>

                    <!-- Level 3 -->
                    <div class="bento-card rounded-3xl p-8">
                        <div class="highlight-green rounded-2xl p-6 mb-6 text-center">
                            <i class="fas fa-microchip text-4xl text-white mb-4"></i>
                            <div class="text-6xl font-black text-white">03</div>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-900 mb-2">芯片供应商国籍</h3>
                        <p class="text-sm text-gray-500 mb-4">Nationality of AI Accelerator Vendors</p>
                        <p class="text-gray-700 leading-relaxed mb-4">
                            最细粒度层面，专注于控制驱动数据中心的先进AI芯片（加速器）供应链。NVIDIA在这一领域占据主导地位。
                        </p>
                        <div class="bg-gray-50 rounded-xl p-4">
                            <div class="text-center mb-3">
                                <div class="text-3xl font-bold text-green-600">95.5%</div>
                                <div class="text-sm text-gray-600">美国芯片驱动</div>
                            </div>
                            <p class="text-xs text-gray-600 text-center">
                                NVIDIA占据80-95%的AI加速器市场份额，形成近乎垄断
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Empirical Findings -->
    <section class="py-16 bg-gray-100">
        <div class="container mx-auto px-6">
            <div class="max-w-6xl mx-auto">
                <div class="text-center mb-12">
                    <h2 class="large-text text-gray-900 mb-6">实证研究发现</h2>
                    <p class="text-xl text-gray-600">Empirical Findings from Global Census of Public AI Compute</p>
                </div>

                <!-- Methodology -->
                <div class="bento-card rounded-3xl p-8 mb-12">
                    <h3 class="text-2xl font-bold text-gray-900 mb-6">研究方法 Methodology</h3>
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <div>
                            <p class="text-gray-700 leading-relaxed mb-4">
                                本研究对九家领先云服务提供商的公共云区域和可用AI相关加速器进行了普查：<strong>美国</strong>（AWS、Google、Microsoft）、<strong>中国</strong>（阿里巴巴、华为、腾讯）、<strong>欧洲</strong>（Exoscale、Hetzner、OVHCloud）。
                            </p>
                            <p class="text-gray-700 leading-relaxed">
                                "公共AI计算"指的是商业可用的基于云的计算基础设施，这些基础设施配备了AI训练和推理所需的专用加速器硬件。
                            </p>
                        </div>
                        <div>
                            <p class="text-gray-700 leading-relaxed mb-4">
                                The study conducted a census of public cloud regions and available AI-relevant accelerators from nine leading providers across three major regions.
                            </p>
                            <p class="text-gray-700 leading-relaxed">
                                This comprehensive analysis provides the first systematic mapping of global AI compute distribution and sovereignty patterns.
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Level 1 Findings -->
                <div class="bento-card rounded-3xl p-8 mb-8">
                    <div class="flex items-center mb-6">
                        <div class="highlight-blue rounded-xl p-3 mr-4">
                            <i class="fas fa-map-marker-alt text-2xl text-white"></i>
                        </div>
                        <div>
                            <h3 class="text-2xl font-bold text-gray-900">第一层：领土管辖权发现</h3>
                            <p class="text-gray-600">Level 1: Territorial Jurisdiction Findings</p>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                        <div>
                            <h4 class="text-lg font-semibold text-gray-900 mb-3">集中分布 Concentrated Distribution</h4>
                            <p class="text-gray-700 leading-relaxed mb-4">
                                在43个国家的225个云区域调查中，只有33个国家的132个加速器启用区域被发现。这意味着世界上只有少数国家拥有任何公共云AI计算能力。
                            </p>
                            <div class="bg-blue-50 rounded-xl p-4">
                                <div class="text-center">
                                    <div class="text-3xl font-bold text-blue-600">132/225</div>
                                    <div class="text-sm text-gray-600">加速器启用区域比例</div>
                                </div>
                            </div>
                        </div>

                        <div>
                            <h4 class="text-lg font-semibold text-gray-900 mb-3">全球差异 Global Disparity</h4>
                            <p class="text-gray-700 leading-relaxed mb-4">
                                在这33个国家中，只有24个拥有训练相关的AI计算，约占联合国成员国的12%。这突出了"计算拥有者和计算缺乏者"之间这一战略资源分布的显著全球差异。
                            </p>
                            <div class="bg-purple-50 rounded-xl p-4">
                                <div class="text-center">
                                    <div class="text-3xl font-bold text-purple-600">24/193</div>
                                    <div class="text-sm text-gray-600">训练级计算国家占比</div>
                                </div>
                            </div>
                        </div>

                        <div>
                            <h4 class="text-lg font-semibold text-gray-900 mb-3">地理集中 Geographic Concentration</h4>
                            <p class="text-gray-700 leading-relaxed mb-4">
                                主机国家主要位于北美、欧洲、中东和亚洲。美国以26个计算区域（22个训练相关）领先，其次是中国的22个区域（4个训练相关，22个推理相关）。
                            </p>
                            <div class="bg-green-50 rounded-xl p-4">
                                <div class="grid grid-cols-2 gap-2 text-center">
                                    <div>
                                        <div class="text-2xl font-bold text-green-600">26</div>
                                        <div class="text-xs text-gray-600">美国区域</div>
                                    </div>
                                    <div>
                                        <div class="text-2xl font-bold text-green-600">22</div>
                                        <div class="text-xs text-gray-600">中国区域</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Chart Section -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <div class="bento-card rounded-3xl p-8">
                        <h3 class="text-2xl font-bold text-gray-900 mb-6">全球分布图表</h3>
                        <div class="chart-container">
                            <canvas id="distributionChart"></canvas>
                        </div>
                    </div>

                    <div class="bento-card rounded-3xl p-8">
                        <h3 class="text-2xl font-bold text-gray-900 mb-6">区域分布详情</h3>
                        <div class="space-y-4">
                            <div class="flex justify-between items-center p-3 bg-blue-50 rounded-xl">
                                <div>
                                    <div class="font-semibold text-gray-900">美国 United States</div>
                                    <div class="text-sm text-gray-600">26个区域，22个训练级</div>
                                </div>
                                <div class="text-2xl font-bold text-blue-600">26</div>
                            </div>

                            <div class="flex justify-between items-center p-3 bg-red-50 rounded-xl">
                                <div>
                                    <div class="font-semibold text-gray-900">中国 China</div>
                                    <div class="text-sm text-gray-600">22个区域，4个训练级</div>
                                </div>
                                <div class="text-2xl font-bold text-red-600">22</div>
                            </div>

                            <div class="flex justify-between items-center p-3 bg-green-50 rounded-xl">
                                <div>
                                    <div class="font-semibold text-gray-900">欧洲 Europe</div>
                                    <div class="text-sm text-gray-600">多国分布</div>
                                </div>
                                <div class="text-2xl font-bold text-green-600">15+</div>
                            </div>

                            <div class="flex justify-between items-center p-3 bg-orange-50 rounded-xl">
                                <div>
                                    <div class="font-semibold text-gray-900">其他 Others</div>
                                    <div class="text-sm text-gray-600">亚洲、中东等</div>
                                </div>
                                <div class="text-2xl font-bold text-orange-600">12+</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Level 2 & 3 Findings -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-6">
            <div class="max-w-6xl mx-auto">
                <!-- Level 2 Findings -->
                <div class="bento-card rounded-3xl p-8 mb-12">
                    <div class="flex items-center mb-6">
                        <div class="highlight-purple rounded-xl p-3 mr-4">
                            <i class="fas fa-building text-2xl text-white"></i>
                        </div>
                        <div>
                            <h3 class="text-2xl font-bold text-gray-900">第二层：云服务商国籍发现</h3>
                            <p class="text-gray-600">Level 2: AI Compute Provider Nationality</p>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                        <div>
                            <h4 class="text-lg font-semibold text-gray-900 mb-4">超大规模云服务商主导</h4>
                            <p class="text-gray-700 leading-relaxed mb-4">
                                全球公共AI计算市场由少数大型提供商主导，主要是<strong>美国</strong>（AWS、Google Cloud、Microsoft Azure）和<strong>中国</strong>（阿里巴巴云、华为云、腾讯云）公司。这种集中度反映了AI基础设施的高技术和资本门槛。
                            </p>
                            <p class="text-gray-700 leading-relaxed">
                                只有美国、中国和少数欧洲国家由于拥有国内云提供商而符合这一定义下的计算主权。奥地利是唯一完全依赖欧洲提供商的国家。
                            </p>
                        </div>
                        <div>
                            <h4 class="text-lg font-semibold text-gray-900 mb-4">Hyperscaler Dominance</h4>
                            <p class="text-gray-700 leading-relaxed mb-4">
                                The global market for public AI compute is dominated by a small number of large providers, primarily US and Chinese corporations. This concentration reflects the high technical and capital barriers to AI infrastructure.
                            </p>
                            <p class="text-gray-700 leading-relaxed">
                                Only the US, China, and a handful of European countries qualify as compute sovereign under this definition due to their domestically owned cloud providers.
                            </p>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <!-- Aligning Countries -->
                        <div class="bg-blue-50 rounded-2xl p-6">
                            <h4 class="text-xl font-bold text-gray-900 mb-4">对齐策略 "Aligning"</h4>
                            <p class="text-gray-700 leading-relaxed mb-4">
                                <strong>18个国家</strong>依赖来自单一外国国籍的提供商（美国或中国）。例如澳大利亚、日本、以色列（美国对齐）和智利、印度尼西亚、沙特阿拉伯（中国对齐）。
                            </p>
                            <p class="text-gray-700 leading-relaxed">
                                这通常反映更广泛的政治或防务联盟，将外国云提供商视为"可信国家技术生态系统的延伸"。
                            </p>
                            <div class="mt-4 text-center">
                                <div class="text-3xl font-bold text-blue-600">18</div>
                                <div class="text-sm text-gray-600">单一对齐国家</div>
                            </div>
                        </div>

                        <!-- Hedging Countries -->
                        <div class="bg-purple-50 rounded-2xl p-6">
                            <h4 class="text-xl font-bold text-gray-900 mb-4">对冲策略 "Hedging"</h4>
                            <p class="text-gray-700 leading-relaxed mb-4">
                                <strong>12个国家</strong>托管多个国籍的提供商。例如，新加坡托管3个美国提供商的AI计算区域和3个中国提供商的区域。
                            </p>
                            <p class="text-gray-700 leading-relaxed">
                                这展示了通过多元化实现"战略自主"的策略，减少对单一外国技术生态系统的依赖。
                            </p>
                            <div class="mt-4 text-center">
                                <div class="text-3xl font-bold text-purple-600">12</div>
                                <div class="text-sm text-gray-600">多元化对冲国家</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Level 3 Findings -->
                <div class="bento-card rounded-3xl p-8">
                    <div class="flex items-center mb-6">
                        <div class="highlight-green rounded-xl p-3 mr-4">
                            <i class="fas fa-microchip text-2xl text-white"></i>
                        </div>
                        <div>
                            <h3 class="text-2xl font-bold text-gray-900">第三层：AI加速器芯片供应商国籍</h3>
                            <p class="text-gray-600">Level 3: AI Accelerator Chip Vendor Nationality</p>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                        <div>
                            <h4 class="text-lg font-semibold text-gray-900 mb-3">NVIDIA近乎垄断</h4>
                            <p class="text-gray-700 leading-relaxed mb-4">
                                美国公司NVIDIA在销售给云提供商的AI加速器市场中占据<strong>80-95%</strong>的主导份额。这种近乎垄断的地位使美国在全球AI计算基础设施中拥有巨大影响力。
                            </p>
                            <div class="bg-green-50 rounded-xl p-4">
                                <div class="text-center">
                                    <div class="text-3xl font-bold text-green-600">80-95%</div>
                                    <div class="text-sm text-gray-600">NVIDIA市场份额</div>
                                </div>
                            </div>
                        </div>

                        <div>
                            <h4 class="text-lg font-semibold text-gray-900 mb-3">美国在加速器领域的主导地位</h4>
                            <p class="text-gray-700 leading-relaxed mb-4">
                                在普查的132个加速器启用区域中，<strong>95.5%</strong>由美国拥有的加速器驱动。这种压倒性的主导地位突出了美国在AI硬件供应链中的关键地位。
                            </p>
                            <div class="bg-green-50 rounded-xl p-4">
                                <div class="text-center">
                                    <div class="text-3xl font-bold text-green-600">95.5%</div>
                                    <div class="text-sm text-gray-600">美国芯片驱动区域</div>
                                </div>
                            </div>
                        </div>

                        <div>
                            <h4 class="text-lg font-semibold text-gray-900 mb-3">有限的国内芯片生产</h4>
                            <p class="text-gray-700 leading-relaxed mb-4">
                                只有美国和中国托管来自国内芯片供应商的AI加速器。中国激励国内AI加速器设计和制造，华为与中芯国际合作开发先进芯片，阿里巴巴和腾讯也在追求自己的AI加速器项目。
                            </p>
                            <div class="bg-green-50 rounded-xl p-4">
                                <div class="text-center">
                                    <div class="text-3xl font-bold text-green-600">2</div>
                                    <div class="text-sm text-gray-600">拥有国产芯片的国家</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-8 bg-gray-50 rounded-2xl p-6">
                        <h4 class="text-lg font-semibold text-gray-900 mb-4">"友岸外包"作为策略</h4>
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <p class="text-gray-700 leading-relaxed mb-4">
                                    对大多数国家来说，在AI加速器生产方面实现自给自足是不现实的。相反，策略围绕"友岸外包"或选择依赖哪些外国AI加速器芯片供应商。
                                </p>
                                <p class="text-gray-700 leading-relaxed">
                                    除了法国、新加坡和阿联酋（拥有一些华为昇腾加速器）外，绝大多数国家在这一层面的计算主权上都与美国"对齐"。
                                </p>
                            </div>
                            <div>
                                <p class="text-gray-700 leading-relaxed mb-4">
                                    For most countries, achieving autarky in AI accelerator production is unrealistic. Instead, strategies revolve around "friendshoring" or choosing which foreign AI accelerator chip vendors to depend on.
                                </p>
                                <p class="text-gray-700 leading-relaxed">
                                    NVIDIA actively positions itself as a partner in countries' pursuit of "sovereign AI capabilities," further cementing its dominant position.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Key Findings -->
    <section class="py-20 bg-gray-900 text-white">
        <div class="container mx-auto px-6">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <div class="lg:col-span-2 parallax-element" data-speed="0.1">
                    <h2 class="large-text mb-8">核心发现</h2>
                    <p class="text-xl text-gray-300 mb-8">Key Empirical Findings</p>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="glass-effect rounded-2xl p-6">
                            <div class="text-4xl font-bold text-blue-400 mb-2">80-95%</div>
                            <h3 class="text-lg font-semibold mb-2">NVIDIA市场份额</h3>
                            <p class="text-sm text-gray-300">在AI加速器市场的近乎垄断地位</p>
                        </div>

                        <div class="glass-effect rounded-2xl p-6">
                            <div class="text-4xl font-bold text-purple-400 mb-2">24</div>
                            <h3 class="text-lg font-semibold mb-2">训练级计算国家</h3>
                            <p class="text-sm text-gray-300">拥有训练相关AI计算能力的国家数量</p>
                        </div>

                        <div class="glass-effect rounded-2xl p-6">
                            <div class="text-4xl font-bold text-green-400 mb-2">9</div>
                            <h3 class="text-lg font-semibold mb-2">主要云服务商</h3>
                            <p class="text-sm text-gray-300">美国、中国、欧洲的领先公共云提供商</p>
                        </div>

                        <div class="glass-effect rounded-2xl p-6">
                            <div class="text-4xl font-bold text-orange-400 mb-2">43</div>
                            <h3 class="text-lg font-semibold mb-2">覆盖国家总数</h3>
                            <p class="text-sm text-gray-300">研究涵盖的全球国家数量</p>
                        </div>
                    </div>
                </div>

                <div class="parallax-element" data-speed="0.15">
                    <div class="glass-effect rounded-3xl p-8">
                        <h3 class="text-2xl font-bold mb-6">地理分布</h3>
                        <div class="space-y-4">
                            <div class="flex justify-between items-center">
                                <span class="text-gray-300">北美</span>
                                <div class="flex items-center space-x-2">
                                    <div class="w-20 bg-gray-700 rounded-full h-2">
                                        <div class="bg-blue-400 h-2 rounded-full" style="width: 90%"></div>
                                    </div>
                                    <span class="text-sm">90%</span>
                                </div>
                            </div>

                            <div class="flex justify-between items-center">
                                <span class="text-gray-300">亚洲</span>
                                <div class="flex items-center space-x-2">
                                    <div class="w-20 bg-gray-700 rounded-full h-2">
                                        <div class="bg-purple-400 h-2 rounded-full" style="width: 75%"></div>
                                    </div>
                                    <span class="text-sm">75%</span>
                                </div>
                            </div>

                            <div class="flex justify-between items-center">
                                <span class="text-gray-300">欧洲</span>
                                <div class="flex items-center space-x-2">
                                    <div class="w-20 bg-gray-700 rounded-full h-2">
                                        <div class="bg-green-400 h-2 rounded-full" style="width: 60%"></div>
                                    </div>
                                    <span class="text-sm">60%</span>
                                </div>
                            </div>

                            <div class="flex justify-between items-center">
                                <span class="text-gray-300">中东</span>
                                <div class="flex items-center space-x-2">
                                    <div class="w-20 bg-gray-700 rounded-full h-2">
                                        <div class="bg-orange-400 h-2 rounded-full" style="width: 30%"></div>
                                    </div>
                                    <span class="text-sm">30%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Policy Implications -->
    <section class="py-20 bg-white">
        <div class="container mx-auto px-6">
            <div class="text-center mb-16 parallax-element" data-speed="0.1">
                <h2 class="large-text text-gray-900 mb-6">政策影响</h2>
                <p class="text-xl text-gray-600">Policy Implications and Trade-offs</p>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
                <!-- Benefits -->
                <div class="parallax-element" data-speed="0.05">
                    <div class="bento-card rounded-3xl p-8">
                        <div class="highlight-green rounded-2xl p-6 mb-6 text-center">
                            <i class="fas fa-check-circle text-4xl text-white mb-2"></i>
                            <h3 class="text-2xl font-bold text-white">优势</h3>
                            <p class="text-sm text-green-100">Benefits</p>
                        </div>

                        <div class="space-y-4">
                            <div class="flex items-start space-x-3">
                                <i class="fas fa-shield-alt text-green-600 mt-1"></i>
                                <div>
                                    <h4 class="font-semibold text-gray-900">供应安全</h4>
                                    <p class="text-sm text-gray-600">增强对关键AI基础设施的控制</p>
                                </div>
                            </div>

                            <div class="flex items-start space-x-3">
                                <i class="fas fa-gavel text-green-600 mt-1"></i>
                                <div>
                                    <h4 class="font-semibold text-gray-900">司法管辖</h4>
                                    <p class="text-sm text-gray-600">数据隐私和安全法规的有效执行</p>
                                </div>
                            </div>

                            <div class="flex items-start space-x-3">
                                <i class="fas fa-chart-line text-green-600 mt-1"></i>
                                <div>
                                    <h4 class="font-semibold text-gray-900">经济发展</h4>
                                    <p class="text-sm text-gray-600">税收收入、建设就业和可再生能源投资</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Drawbacks -->
                <div class="parallax-element" data-speed="0.1">
                    <div class="bento-card rounded-3xl p-8">
                        <div class="highlight-orange rounded-2xl p-6 mb-6 text-center">
                            <i class="fas fa-exclamation-triangle text-4xl text-white mb-2"></i>
                            <h3 class="text-2xl font-bold text-white">挑战</h3>
                            <p class="text-sm text-orange-100">Drawbacks</p>
                        </div>

                        <div class="space-y-4">
                            <div class="flex items-start space-x-3">
                                <i class="fas fa-bolt text-orange-600 mt-1"></i>
                                <div>
                                    <h4 class="font-semibold text-gray-900">资源消耗</h4>
                                    <p class="text-sm text-gray-600">大量能源、水资源和土地需求</p>
                                </div>
                            </div>

                            <div class="flex items-start space-x-3">
                                <i class="fas fa-users text-orange-600 mt-1"></i>
                                <div>
                                    <h4 class="font-semibold text-gray-900">就业创造有限</h4>
                                    <p class="text-sm text-gray-600">相对于资源投入的就业机会较少</p>
                                </div>
                            </div>

                            <div class="flex items-start space-x-3">
                                <i class="fas fa-cogs text-orange-600 mt-1"></i>
                                <div>
                                    <h4 class="font-semibold text-gray-900">监管复杂性</h4>
                                    <p class="text-sm text-gray-600">与外国云服务商的法规协调难题</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Conclusion -->
    <section class="py-20 gradient-bg text-white">
        <div class="container mx-auto px-6 text-center">
            <div class="parallax-element" data-speed="0.1">
                <h2 class="large-text mb-8">结论与展望</h2>
                <p class="text-xl mb-8 opacity-90">Conclusion and Future Outlook</p>

                <div class="max-w-4xl mx-auto">
                    <p class="text-lg leading-relaxed mb-8 opacity-90">
                        计算主权不是一个单一的二元条件，而是一个多层面的概念，具有不同的解释和影响。
                        实证数据揭示了AI计算分布的全球不对称性，少数"计算拥有者"高度集中。
                        各国政府在追求计算主权时面临复杂的权衡和战略选择。
                    </p>

                    <div class="glass-effect rounded-2xl p-8 max-w-2xl mx-auto">
                        <h3 class="text-2xl font-bold mb-4">关键洞察</h3>
                        <p class="text-lg opacity-90">
                            计算主权的追求是一个"由潜在价值观和权力动态塑造的政治项目"，
                            需要政策制定者的仔细考虑和批判性分析。
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="container mx-auto px-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div>
                    <h3 class="text-xl font-bold mb-4">研究来源</h3>
                    <p class="text-gray-400 text-sm">
                        基于"ssrn-5312977.pdf"研究报告<br>
                        发布日期：2024-11-20
                    </p>
                </div>

                <div>
                    <h3 class="text-xl font-bold mb-4">数据覆盖</h3>
                    <p class="text-gray-400 text-sm">
                        九大云服务提供商<br>
                        43个国家，225个云区域
                    </p>
                </div>

                <div>
                    <h3 class="text-xl font-bold mb-4">分析框架</h3>
                    <p class="text-gray-400 text-sm">
                        三层计算主权概念<br>
                        地缘政治视角分析
                    </p>
                </div>
            </div>

            <div class="border-t border-gray-800 mt-8 pt-8 text-center">
                <p class="text-gray-400 text-sm">
                    © 2024 AI计算主权研究报告 | 基于学术研究的可视化展示
                </p>
            </div>
        </div>
    </footer>

    <script>
        // Scroll Progress Indicator
        window.addEventListener('scroll', () => {
            const scrollProgress = document.getElementById('scrollProgress');
            const scrollPercent = (window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100;
            scrollProgress.style.transform = `scaleX(${scrollPercent / 100})`;
        });

        // Parallax Effect
        window.addEventListener('scroll', () => {
            const parallaxElements = document.querySelectorAll('.parallax-element');
            const scrollTop = window.pageYOffset;

            parallaxElements.forEach(element => {
                const speed = element.dataset.speed || 0.1;
                const yPos = -(scrollTop * speed);
                element.style.transform = `translateY(${yPos}px)`;
            });
        });

        // Chart.js - Distribution Chart
        const ctx = document.getElementById('distributionChart').getContext('2d');
        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['美国', '中国', '欧洲', '其他'],
                datasets: [{
                    data: [26, 22, 15, 12],
                    backgroundColor: [
                        'rgba(59, 130, 246, 0.8)',
                        'rgba(239, 68, 68, 0.8)',
                        'rgba(34, 197, 94, 0.8)',
                        'rgba(156, 163, 175, 0.8)'
                    ],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            font: {
                                size: 14
                            }
                        }
                    }
                }
            }
        });

        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe all bento cards
        document.querySelectorAll('.bento-card').forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            card.style.transition = 'all 0.6s ease';
            observer.observe(card);
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>
