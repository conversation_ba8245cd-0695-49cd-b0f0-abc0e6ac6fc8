<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Augment Code - 革命性的AI编程平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 1rem 0;
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }
        
        nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 1.8rem;
            font-weight: bold;
            color: #667eea;
        }
        
        .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
        }
        
        .nav-links a {
            text-decoration: none;
            color: #333;
            font-weight: 500;
            transition: color 0.3s;
        }
        
        .nav-links a:hover {
            color: #667eea;
        }
        
        .hero {
            padding: 120px 0 80px;
            text-align: center;
            color: white;
        }
        
        .hero h1 {
            font-size: 3.5rem;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .hero p {
            font-size: 1.3rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        
        .cta-button {
            display: inline-block;
            background: #ff6b6b;
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 50px;
            font-weight: bold;
            font-size: 1.1rem;
            transition: transform 0.3s, box-shadow 0.3s;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
        }
        
        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.6);
        }
        
        .features {
            background: white;
            padding: 80px 0;
        }
        
        .features h2 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 3rem;
            color: #333;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }
        
        .feature-card {
            background: #f8f9fa;
            padding: 2rem;
            border-radius: 15px;
            text-align: center;
            transition: transform 0.3s, box-shadow 0.3s;
            border: 1px solid #e9ecef;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        
        .feature-card h3 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: #333;
        }
        
        .pricing {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            padding: 80px 0;
            color: white;
        }
        
        .pricing h2 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 3rem;
        }
        
        .pricing-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
        }
        
        .pricing-card {
            background: rgba(255, 255, 255, 0.95);
            color: #333;
            padding: 2rem;
            border-radius: 15px;
            text-align: center;
            position: relative;
            transition: transform 0.3s;
        }
        
        .pricing-card:hover {
            transform: scale(1.05);
        }
        
        .pricing-card.popular {
            border: 3px solid #ff6b6b;
        }
        
        .popular-badge {
            position: absolute;
            top: -10px;
            left: 50%;
            transform: translateX(-50%);
            background: #ff6b6b;
            color: white;
            padding: 5px 20px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: bold;
        }
        
        .price {
            font-size: 2.5rem;
            font-weight: bold;
            color: #667eea;
            margin: 1rem 0;
        }
        
        .price-period {
            font-size: 1rem;
            color: #666;
        }
        
        .features-list {
            list-style: none;
            margin: 1.5rem 0;
        }
        
        .features-list li {
            padding: 0.5rem 0;
            border-bottom: 1px solid #eee;
        }
        
        .features-list li:before {
            content: "✓";
            color: #28a745;
            font-weight: bold;
            margin-right: 10px;
        }
        
        footer {
            background: #333;
            color: white;
            text-align: center;
            padding: 2rem 0;
        }
        
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2.5rem;
            }
            
            .nav-links {
                display: none;
            }
            
            .features-grid,
            .pricing-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <header>
        <nav class="container">
            <div class="logo">Augment Code</div>
            <ul class="nav-links">
                <li><a href="#features">功能特色</a></li>
                <li><a href="#pricing">价格方案</a></li>
                <li><a href="#contact">联系我们</a></li>
            </ul>
        </nav>
    </header>

    <section class="hero">
        <div class="container">
            <h1>革命性的AI编程平台</h1>
            <p>更好的上下文理解，更智能的AI助手，更高质量的代码</p>
            <p>业界领先的上下文引擎，让AI真正理解你的代码库</p>
            <a href="#pricing" class="cta-button">立即开始 14 天免费试用</a>
        </div>
    </section>

    <section class="features" id="features">
        <div class="container">
            <h2>强大功能，助力开发</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">🤖</div>
                    <h3>智能AI助手</h3>
                    <p>深度理解你的代码库，提供端到端的任务完成能力。可以规划、构建并创建PR供你审查。</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">🧠</div>
                    <h3>行业领先的上下文引擎</h3>
                    <p>专有的上下文检索技术，结合前沿模型，为你提供可直接部署到生产环境的高质量代码。</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">💬</div>
                    <h3>智能对话</h3>
                    <p>即时获得答案，无需搜索文档或打扰团队成员。快速解决问题，保持开发流程。</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">✏️</div>
                    <h3>Next Edit 编辑指导</h3>
                    <p>为复杂的代码更改提供逐步指导，处理重构、依赖升级和架构变更。</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">⚡</div>
                    <h3>智能代码补全</h3>
                    <p>基于你的代码库、依赖项和外部API的个性化内联补全，速度极快。</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">🔧</div>
                    <h3>丰富的集成</h3>
                    <p>支持VS Code、JetBrains IDEs、Vim等编辑器，集成GitHub、Jira、Slack等工具。</p>
                </div>
            </div>
        </div>
    </section>

    <section class="pricing" id="pricing">
        <div class="container">
            <h2>简单透明的价格方案</h2>
            <div class="pricing-grid">
                <div class="pricing-card">
                    <h3>Community</h3>
                    <div class="price">$0<span class="price-period">/月</span></div>
                    <ul class="features-list">
                        <li>50条用户消息</li>
                        <li>上下文引擎</li>
                        <li>MCP & 原生工具</li>
                        <li>无限Next Edit & 代码补全</li>
                        <li>社区支持</li>
                    </ul>
                    <a href="#" class="cta-button">立即开始</a>
                </div>
                
                <div class="pricing-card popular">
                    <div class="popular-badge">最受欢迎</div>
                    <h3>Developer</h3>
                    <div class="price">$50<span class="price-period">/月</span></div>
                    <ul class="features-list">
                        <li>600条用户消息</li>
                        <li>团队管理（最多100用户）</li>
                        <li>不使用数据训练</li>
                        <li>SOC 2 Type II认证</li>
                        <li>所有Community功能</li>
                    </ul>
                    <a href="#" class="cta-button">立即开始</a>
                </div>
                
                <div class="pricing-card">
                    <h3>Pro</h3>
                    <div class="price">$100<span class="price-period">/月</span></div>
                    <ul class="features-list">
                        <li>1,500条用户消息</li>
                        <li>社区 & 邮件支持</li>
                        <li>所有Developer功能</li>
                        <li>增强的容量支持</li>
                    </ul>
                    <a href="#" class="cta-button">立即开始</a>
                </div>
                
                <div class="pricing-card">
                    <h3>Max</h3>
                    <div class="price">$250<span class="price-period">/月</span></div>
                    <ul class="features-list">
                        <li>4,500条用户消息</li>
                        <li>高需求团队专用</li>
                        <li>所有Pro功能</li>
                        <li>最大容量支持</li>
                    </ul>
                    <a href="#" class="cta-button">立即开始</a>
                </div>
            </div>
            <div style="text-align: center; margin-top: 2rem;">
                <p style="font-size: 1.1rem;">🎉 所有付费方案都包含 <strong>14天免费试用</strong></p>
                <p style="margin-top: 1rem;">企业方案请联系销售团队获取定制报价</p>
            </div>
        </div>
    </section>

    <footer id="contact">
        <div class="container">
            <p>&copy; 2025 Augment Code. 保留所有权利。</p>
            <p>访问官网：<a href="https://www.augmentcode.com" style="color: #667eea;">www.augmentcode.com</a></p>
        </div>
    </footer>

    <script>
        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });
        
        // 滚动时头部效果
        window.addEventListener('scroll', function() {
            const header = document.querySelector('header');
            if (window.scrollY > 100) {
                header.style.background = 'rgba(255, 255, 255, 0.98)';
            } else {
                header.style.background = 'rgba(255, 255, 255, 0.95)';
            }
        });
    </script>
</body>
</html>
